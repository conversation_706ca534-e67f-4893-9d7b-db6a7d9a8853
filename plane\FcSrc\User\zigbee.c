#include "zigbee.h"
#include "Ano_Math.h"
#include "Drv_Uart.h"  // 包含DrvUart5SendBuf函数声明
#include "mid360.h"    // 包含mid360激光雷达数据结构
#include "User_Task.h" // 包含任务相关变量和函数声明
#include "AnoPTv8FrameFactory.h" // 包含调试信息发送函数
#include <stdio.h>     // 包含sprintf函数
#include "Maixcam.h"
#include "LX_FcFunc.h"
#include "path_storage.h" // 包含预计算路径存储接口




// ================== 外部变量声明 ==================
extern s16 work_pos[63][6];        // 工作点坐标数组
extern u8 BEEP_flag;               // 蜂鸣器标志
extern u8 mission_enabled_flag;    // 任务执行标志
extern u8 zigbee_up_f;             // Zigbee任务执行标志
extern mid360_info mid360;         // mid360激光雷达数据
extern int current_patrol_index;   // 当前巡查索引（来自User_Task.c）
extern u32 mission_start_time_ms;  // 任务开始时间（来自User_Task.c）

// ================== 常量定义 ==================
#define WORK_POINT_ARRAY_SIZE   63      // 工作点数组大小

// ================== 函数声明 ==================
int path_planner_position_code_to_index(int position_code);  // 位置代码转索引函数

// ================== 全局变量定义 ==================
screen_info screen;  // 屏幕数据结构

// ================== 野生动物巡查系统变量 ==================
// 动物类型定义
#define ANIMAL_TYPE_ELEPHANT    1   // 象
#define ANIMAL_TYPE_TIGER       2   // 虎
#define ANIMAL_TYPE_WOLF        3   // 狼
#define ANIMAL_TYPE_MONKEY      4   // 猴


static float coordinate_scale_y = 1.0f;    // Y轴缩放系数
static s16 coordinate_offset_x = 0;        // X轴偏移量
static s16 coordinate_offset_y = 0;        // Y轴偏移量
static bool calibration_applied = false;   // 校准是否已应用标志

// ================== 协议解析相关变量 ==================
static u8 screen_receive_buf[64];          // 接收数据缓冲区(最多64字节，支持禁飞区数据)


/*******************************************************
    函数名称：screen_receiver_GetOneByte
    参  数: const u8 linktype - 链路类型 (LT_U5)
           const u8 data - 接收到的单字节数据
    返  回: 无
    功能说明：处理屏幕协议信息接收解析
    协议格式：AA FF cmd data EA
    cmd:04  data：1个字节 为请求系统状态查询ID (必须为0)
    cmd:05  data：1个字节 为请求航点巡航任务ID (任意值)
********************************************************/
void screen_receiver_GetOneByte(const u8 linktype, const u8 data)
{
    static u8 screen_flag = 0;        // case状态机计数
    static u8 screen_datalen = 0;     // 数据长度计数
    static u8 screen_cmd = 0;         // 当前命令
    static u8 screen_expected_len = 0; // 期望数据长度

    switch(screen_flag)
    {
        case 0: // 等待帧头0xAA
        {
            if(data == 0xAA)
                screen_flag++;
            screen_datalen = 0;
        }
        break;

        case 1: // 等待帧头0xFF
        {
            if(data == 0xFF)
                screen_flag++;
            else
                screen_flag = 0;
        }
        break;

        case 2: // 接收cmd和数据
        {
            if(screen_datalen == 0) // 接收cmd字节
            {
                screen_cmd = data;

                // 根据cmd确定期望的数据长度
                switch(screen_cmd)
                {
                    case 0x01: screen_expected_len = 3; break; // 禁飞区设置命令（3个位置代码）
                    case 0x02: screen_expected_len = 1; break; // 开始巡检起飞命令
                    case 0x03: screen_expected_len = 1; break; // 校准命令（保持不变）
                    default:
                        screen_flag = 0; // 无效cmd，重新开始
                        return;
                }
                screen_datalen++;
            }
            else // 接收数据字节
            {
                screen_receive_buf[screen_datalen-1] = data; // screen_datalen-1因为第一个字节是cmd
                screen_datalen++;

                // 检查是否接收完所有期望的数据字节
                if(screen_datalen == screen_expected_len + 1) // +1因为包含了cmd字节
                {
                    screen_flag++;
                }
            }
        }
        break;

        case 3: // 等待帧尾0xEA
        {
            if(data == 0xEA) // 正确帧尾
            {
							  zigbee_send_screen_data(0x04, 0x04); // 地面站闪烁LED  成功
                // 根据cmd进行数据处理
                switch(screen_cmd)
                {
                    case 0x01: // 禁飞区设置命令 (固定3个位置代码)
                    {
                        screen.cmd = 0x01;
                        screen.id = 3; // 固定为3个禁飞区
                        screen.data_valid = 1;

                        // 调用数据处理业务逻辑函数，传递固定数量3
                        zigbee_screen_data_handler(0x01, 3);
                    }
                    break;

                    case 0x02: // 开始巡检起飞命令
                    {
                        screen.cmd = 0x02;
                        screen.id = screen_receive_buf[0]; // 任务ID
                        screen.data_valid = 1;

                        // 调用数据处理业务逻辑函数
                        zigbee_screen_data_handler(0x02, screen_receive_buf[0]);
                    }
                    break;
										
										
								 case 0x03: // ID 1~3  对应低高度、高高度、终点坐标
                    {
                        screen.cmd = 0x03;
                        screen.id = screen_receive_buf[0]; // 任务ID
                        screen.data_valid = 1;

                        // 调用数据处理业务逻辑函数
                        zigbee_screen_data_handler(0x03, screen_receive_buf[0]);
                    }
                    break;


                    default:
                        break;
                }
            }
            screen_flag = 0;
        }
        break;

        default:
            screen_flag = 0;
            break;
    }
}

/*******************************************************
    函数名称：zigbee_send_screen_data
    参  数: u8 position_id - 位置标识
           u8 type_id - 类型标识
    返  回: 无
    功能说明：向串口发送回复数据
    协议格式：AA FF position_id type_id EA
********************************************************/
void zigbee_send_screen_data(u8 position_id, u8 type_id)
{
    u8 send_buf[5] = {0xAA, 0xFF, position_id, type_id, 0xEA};

    // 通过UART5发送回复数据包
    DrvUart5SendBuf(send_buf, 5);
}

/*******************************************************
    函数名称：zigbee_send_screen_data
    参  数: u8 position_id - 位置标识
           u8 type_id - 类型标识
    返  回: 无
    功能说明：向串口发送回复数据
    协议格式：AA FF position_id type_id EA
********************************************************/
void zigbee_send_screen_animal(u8 position_id, u8 type_id, u8 count)
{
    u8 send_buf[6] = {0xAA, 0xFF, position_id, type_id, count, 0xEA};

    // 通过UART5发送回复数据包
    DrvUart5SendBuf(send_buf, 6);
}

/*******************************************************
    函数名称：zigbee_screen_data_handler
    参  数: const u8 cmd - 接收到的命令类型
           const u8 id - 相关的ID
    返  回: 无
    功能说明：处理接收到数据的业务逻辑函数
    根据接收到的CMD和ID，执行相应的业务逻辑处理
********************************************************/
void zigbee_screen_data_handler(const u8 cmd, const u8 id)
{
    switch(cmd)
    {
        case 0x01: // 禁飞区设置命令 (固定3个位置代码)
        {
            // 2025年电赛H题：固定3个连续方格禁飞区
            if (id == 3) // 验证固定数量
            {
                // 调用禁飞区处理函数，直接传递3个位置代码（从索引0开始，因为没有数量字节）
                zigbee_process_no_fly_zones(&screen_receive_buf[0], 3);

                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "CMD 0x01 - No-fly zone setup successful");
            }
            else
            {
                // 数据验证失败：禁飞区数量无效
                AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)id, "Invalid no-fly zone count:");

                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "No-fly zone count out of range!");
            }
        }
        break;

        case 0x02: // 开始巡检起飞命令
        {
					if(id == 0x01)
					{
            // 检查当前任务状态，避免重复启动
            if (zigbee_up_f == 0 && mission_enabled_flag == 0)
            {
                // 设置Zigbee任务执行标志
                zigbee_up_f = 1;


                // 调试信息输出
                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "CMD 0x02 - Patrol mission started");
            }
            else
            {
                AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Mission already running!");
            }
        }
			}
        break;

        case 0x03: // 坐标系校准功能
        {
            switch(id)
            {
                case 1: // ID=1: 高度1
                {
                    zigbee_send_screen_data(0x04, 0x04); // 成功接收到
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Height1 recorded!");
                }
                break;

                case 2: // ID=2: 高度2
                {
                    zigbee_send_screen_data(0x04, 0x04); // 成功接收到
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Height2 recorded!");
                }
                break;


                case 3: // ID=4: 两点校准法 - 设置第一个校准点（原点）
                {
                        zigbee_set_calibration_point_1(mid360.pose_x_cm, mid360.pose_y_cm);
                        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Calib point 1 set!");
                }
                break;

                case 4: // ID=5: 两点校准法 - 设置第二个校准点并执行校准
                {
                    // 执行两点校准，使用当前位置作为第二个校准点
                    if (zigbee_execute_two_point_calibration(mid360.pose_x_cm, mid360.pose_y_cm)) {
                        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Two point calibration done!");
                    } else {
                        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Calibration failed!");
                    }
                }
                break;

                default:
                {
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Invalid calibration ID!");
                }
                break;
            }
        }
        break;

        default:
        {
            // 未知命令处理
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Unknown zigbee command!");
        }
        break;
    }
}






// ================== 坐标校准相关变量和函数 ==================
static s16 calib_point1_mid360_x = 0;     // 第一个校准点mid360坐标
static s16 calib_point1_mid360_y = 0;

static s16 calib_point1_actual_x = 0;      // 第一个校准点实际坐标（通常对应于原点0,0，所以这里没必要的，肯定是00
static s16 calib_point1_actual_y = 0;

static s16 calib_point2_actual_x = 350;      // 第二个校准点实际坐标（通常对应于原点350,200，这里可能会变
static s16 calib_point2_actual_y = 200;

static bool calib_point1_set = false;      // 第一个校准点是否已设置

/*******************************************************
    函数名称：zigbee_set_calibration_point_1
    参  数: s16 mid360_x - 第一个校准点mid360 X坐标
           s16 mid360_y - 第一个校准点mid360 Y坐标
    返  回: 无
    功能说明：设置两点校准法的第一个校准点（通常是原点）
    使用场合：无人机处于已知的第一个校准点位置
********************************************************/
void zigbee_set_calibration_point_1(s16 mid360_x, s16 mid360_y)
{
    calib_point1_mid360_x = mid360_x;
    calib_point1_mid360_y = mid360_y;
    calib_point1_set = true;

    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)mid360_x, "Calib P1 MID360 X:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)mid360_y, "Calib P1 MID360 Y:");
}

/*******************************************************
    函数名称：zigbee_execute_two_point_calibration
    参  数: s16 mid360_x2 - 第二个校准点mid360 X坐标
           s16 mid360_y2 - 第二个校准点mid360 Y坐标
    返  回: bool - 校准是否成功
    功能说明：执行两点校准，计算缩放和偏移参数并应用到work_pos
    使用场合：无人机处于已知的第二个校准点位置后执行
********************************************************/
bool zigbee_execute_two_point_calibration(s16 mid360_x2, s16 mid360_y2)
{
    if(!calib_point1_set)
    {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Calib point 1 not set!");
        return false;
    }


    // 计算mid360坐标系两点间的距离
    float mid360_dx = (float)(mid360_x2 - calib_point1_mid360_x);
    float mid360_dy = (float)(mid360_y2 - calib_point1_mid360_y);

    // 计算实际坐标系两点间的距离
    float actual_dx = (float)(calib_point2_actual_x - calib_point1_actual_x);
    float actual_dy = (float)(calib_point2_actual_y - calib_point1_actual_y);

    // 防止除零错误
    if(ABS(mid360_dx) < 1.0f || ABS(mid360_dy) < 1.0f)
    {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Calib points too close!");
        return false;
    }

    // 输出第二个校准点信息供调试
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)mid360_x2, "Calib P2 MID360 X:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)mid360_y2, "Calib P2 MID360 Y:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, mid360_dx, "MID360 DX:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, mid360_dy, "MID360 DY:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, actual_dx, "Actual DX:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, actual_dy, "Actual DY:");

    // 计算缩放系数 - 修复：应该是实际距离/MID360距离
    coordinate_scale_x = ABS(actual_dx / mid360_dx);
    coordinate_scale_y = ABS(actual_dy / mid360_dy);

    // 简化校准：只使用缩放系数，偏移由home_pos机制处理
    coordinate_offset_x = 0;
    coordinate_offset_y = 0;

    // 应用校准参数到所有work_pos
    zigbee_apply_coordinate_calibration();

    // 输出校准参数供调试
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, coordinate_scale_x, "Scale X:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, coordinate_scale_y, "Scale Y:");
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Simplified calibration: scale only!");

    return true;
}

/*******************************************************
    函数名称：zigbee_apply_coordinate_calibration
    参  数: 无
    返  回: 无
    功能说明：将计算出的缩放和偏移参数应用到所有work_pos数组
    使用场合：坐标校准完成后应用校准参数
********************************************************/
void zigbee_apply_coordinate_calibration(void)
{
    // 防止重复应用校准
    if(calibration_applied)
    {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Calibration already applied!");
        return;
    }

    for(int i = 0; i < 50; i++)
    {
        if(work_pos[i][0] != 0 || work_pos[i][1] != 0 ||
           work_pos[i][2] != 0 || work_pos[i][3] != 0) // 检查是否为有效坐标点
        {
            // 简化校准：只应用缩放变换，偏移由home_pos机制处理
            work_pos[i][0] = (s16)(work_pos[i][0] * coordinate_scale_x);
            work_pos[i][1] = (s16)(work_pos[i][1] * coordinate_scale_y);

        }
    }

    // 标记校准已应用
    calibration_applied = true;

    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Scale calibration applied!");

    // 发送关键点坐标校准结果供调试验证
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)work_pos[0][0], "Start X:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)work_pos[0][1], "Start Y:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)work_pos[28][0], "End X:");
    AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)work_pos[28][1], "End Y:");
}

/*******************************************************
    函数名称：zigbee_reset_coordinate_calibration
    参  数: 无
    返  回: 无
    功能说明：重置坐标缩放校准参数，允许重新校准
    使用场合：需要重新校准时调用
********************************************************/
void zigbee_reset_coordinate_calibration(void)
{
    coordinate_scale_x = 1.0f;
    coordinate_scale_y = 1.0f;
    coordinate_offset_x = 0;
    coordinate_offset_y = 0;
    calibration_applied = false;
    calib_point1_set = false;

    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, "Calibration reset!");
}

// simple_navigation_update函数已删除 - 专注于野生动物巡查系统
// 以下代码块将被完全移除
// 函数体已完全删除

/*******************************************************
    函数名称：zigbee_process_no_fly_zones
    参  数: const u8* position_data - 位置信息数据指针
           u8 count - 禁飞区数量
    返  回: 无
    功能说明：处理禁飞区数据，更新work_pos数组的status字段
    实现逻辑：先清除所有禁飞区标记，再根据新数据设置禁飞区
********************************************************/
// ================== 禁飞区缓存（性能优化）==================
static u8 g_no_fly_zones[3] = {0};
static u8 g_no_fly_zone_count = 0;

/*******************************************************
    函数名称：zigbee_process_no_fly_zones
    参  数: const u8* position_data - 位置信息数据指针
           u8 count - 禁飞区数量
    返  回: 无
    功能说明：简化的禁飞区处理函数 - 只负责接收和设置禁飞区
    架构优化：移除路径查找逻辑，由User_Task.c在任务开始时处理
********************************************************/
void zigbee_process_no_fly_zones(const u8* position_data, u8 count)
{
    // 参数有效性检查
    if (position_data == NULL)
    {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "No-fly zones: NULL data pointer!");
        return;
    }

    // 1. 清除所有禁飞区标记
    for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++)
    {
        work_pos[i][5] = 0;
    }

    // 重置缓存
    g_no_fly_zone_count = 0;
    for (int i = 0; i < 3; i++)
    {
        g_no_fly_zones[i] = 0;
    }

    // 统计变量
    u8 valid_zones = 0;

    // 2. 设置新的禁飞区（直接使用数值，不进行BCD解码）
    for (u8 i = 0; i < count; i++)
    {
        u8 raw_data = position_data[i];
        u8 position_code = raw_data;  // 直接使用原始数据作为position_code
        int index = path_planner_position_code_to_index((int)position_code);

        // 调试信息：显示解析过程
        char debug_info[80];
        sprintf(debug_info, "Raw:%d -> PC:%d -> Index:%d", raw_data, position_code, index);
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_DEFAULT, debug_info);

        if (index >= 0 && index < WORK_POINT_ARRAY_SIZE)
        {
            work_pos[index][5] = 1;  // 标记为禁飞区
            valid_zones++;
        }
        else
        {
            AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)position_code, "Invalid position code:");
        }
    }

    // 3. 验证连续性（可选）
    if (valid_zones == 3)
    {
        if (!zigbee_validate_continuous_no_fly_zones(position_data, count))
        {
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                          "Competition Error: No-fly zones must be continuous!");
            // 清除无效的禁飞区
            for (int i = 0; i < WORK_POINT_ARRAY_SIZE; i++)
            {
                work_pos[i][5] = 0;
            }
            valid_zones = 0;
        }
        else
        {
            // 更新缓存
            g_no_fly_zone_count = count;

            // ===== 禁飞区数据标准化处理 =====
            // 步骤1：直接读取十进制数值到临时数组（新协议格式：0xAA 0xFF 0x01 33 34 35 0xEA）
            u8 sorted_zones[3];
            for (int i = 0; i < count; i++) {
                // 新协议：直接传输十进制数值，无需BCD解码
                sorted_zones[i] = position_data[i];

                // 数据验证：确保position_code在有效范围内（11-97）
                if (sorted_zones[i] < 11 || sorted_zones[i] > 97) {
                    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED,
                                  "Invalid position_code in no-fly zone data");
                    return; // 数据无效，终止处理
                }
            }

            // 步骤2：输出原始输入日志
            char original_info[64];
            sprintf(original_info, "Original input: %d,%d,%d",
                    sorted_zones[0], sorted_zones[1], sorted_zones[2]);
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_DEFAULT, original_info);

            // 步骤3：3元素冒泡排序（升序标准化）
            for (int i = 0; i < 2; i++) {
                for (int j = i + 1; j < 3; j++) {
                    if (sorted_zones[i] > sorted_zones[j]) {
                        u8 temp = sorted_zones[i];
                        sorted_zones[i] = sorted_zones[j];
                        sorted_zones[j] = temp;
                    }
                }
            }

            // 步骤4：存储标准化后的禁飞区数据
            for (int i = 0; i < count; i++) {
                g_no_fly_zones[i] = sorted_zones[i];
            }

            // 输出最终设置结果
            char zone_info[64];
            sprintf(zone_info, "No-fly zones set: %d,%d,%d",
                   g_no_fly_zones[0], g_no_fly_zones[1], g_no_fly_zones[2]);
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN, zone_info);
        }
    }

    // 显示处理结果
    if (valid_zones == 3)
    {
        AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_GREEN,
                      "No-fly zones configured successfully");
    }
    else if (valid_zones > 0)
    {
        AnoPTv8SendValStr(LT_D_IMU, ANOPTV8DEVID_SWJ, (float)valid_zones, "Partial zones set:");
    }

    // 仅此而已！不要在这里查找路径或部署任务
}

/*******************************************************
    函数名称：zigbee_get_no_fly_zones
    参  数: u8* no_fly_zones - 输出缓冲区（至少3字节）
    返  回: u8 - 禁飞区数量
    功能说明：获取当前缓存的禁飞区数据（性能优化接口）
    使用场景：User_Task.c在任务开始时调用，避免扫描work_pos数组
********************************************************/
u8 zigbee_get_no_fly_zones(u8* no_fly_zones)
{
    if (no_fly_zones == NULL)
    {
        return 0;
    }

    // 复制缓存的禁飞区数据
    for (int i = 0; i < g_no_fly_zone_count && i < 3; i++)
    {
        no_fly_zones[i] = g_no_fly_zones[i];
    }

    return g_no_fly_zone_count;
}

/*******************************************************
    函数名称：zigbee_validate_continuous_no_fly_zones
    参  数: const u8* position_data - 位置信息数据指针
           u8 count - 禁飞区数量
    返  回: bool - true表示连续，false表示不连续
    功能说明：验证三个禁飞区是否连续（竞赛要求）
    连续性定义：三个方格在水平或垂直方向上相邻
********************************************************/
bool zigbee_validate_continuous_no_fly_zones(const u8* position_data, u8 count)
{
    if (count != 3 || position_data == NULL)
    {
        return false;
    }

    // 直接读取三个position_code（新协议：直接传输十进制数值）
    u8 codes[3];
    for (int i = 0; i < 3; i++)
    {
        // 新协议：直接读取十进制数值，无需BCD解码
        codes[i] = position_data[i];
    }

    // 将position_code转换为行列坐标
    // 位置代码格式：AB，其中A是列号，B是行号
    // 例如：92 = A9B2 = 第9列第2行
    u8 rows[3], cols[3];
    for (int i = 0; i < 3; i++)
    {
        cols[i] = codes[i] / 10;  // 列号（十位数字）
        rows[i] = codes[i] % 10;  // 行号（个位数字）



        // 验证坐标有效性
        // 行号：1-7 (B1-B7)，列号：1-9 (A1-A9)
        if (rows[i] < 1 || rows[i] > 7 || cols[i] < 1 || cols[i] > 9)
        {
            AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "Invalid position range!");
            return false;
        }
    }

    // 检查连续性：三个方格必须在同一行或同一列，且相邻
    // 情况1：水平连续（同一行，列号连续）
    if (rows[0] == rows[1] && rows[1] == rows[2])
    {
        // 排序列号
        u8 sorted_cols[3] = {cols[0], cols[1], cols[2]};
        for (int i = 0; i < 2; i++)
        {
            for (int j = i + 1; j < 3; j++)
            {
                if (sorted_cols[i] > sorted_cols[j])
                {
                    u8 temp = sorted_cols[i];
                    sorted_cols[i] = sorted_cols[j];
                    sorted_cols[j] = temp;
                }
            }
        }

        // 检查列号是否连续
        if (sorted_cols[1] == sorted_cols[0] + 1 && sorted_cols[2] == sorted_cols[1] + 1)
        {
            return true;
        }
    }

    // 情况2：垂直连续（同一列，行号连续）
    if (cols[0] == cols[1] && cols[1] == cols[2])
    {
        // 排序行号
        u8 sorted_rows[3] = {rows[0], rows[1], rows[2]};
        for (int i = 0; i < 2; i++)
        {
            for (int j = i + 1; j < 3; j++)
            {
                if (sorted_rows[i] > sorted_rows[j])
                {
                    u8 temp = sorted_rows[i];
                    sorted_rows[i] = sorted_rows[j];
                    sorted_rows[j] = temp;
                }
            }
        }

        // 检查行号是否连续
        if (sorted_rows[1] == sorted_rows[0] + 1 && sorted_rows[2] == sorted_rows[1] + 1)
        {
            return true;
        }
    }

    // 不连续
    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_RED, "No-fly zones are not continuous");
    return false;
}

// zigbee_add_animal_record 函数已删除
// 动物识别数据直接通过 zigbee_send_screen_animal() 发送给地面站
// 统计功能由地面站实现

// zigbee_print_animal_statistics 函数已删除
// 统计功能由地面站实现

// zigbee_clear_animal_records 函数已删除
// 统计功能由地面站实现



/*******************************************************
    函数名称：reset_patrol_order
    参  数: 无
    返  回: 无
    功能说明：重置巡查状态变量
    实现逻辑：重置current_patrol_index和任务开始时间
********************************************************/
void reset_patrol_order(void)
{
    current_patrol_index = 0;                   // 重置巡查索引从0开始
    mission_start_time_ms = GetSysRunTimeMs();  // 重置任务开始时间

    AnoPTv8SendStr(LT_D_IMU, ANOPTV8DEVID_SWJ, ANOLOGCOLOR_DEFAULT,
                  "Patrol state reset");
}


/*******************************************************
    函数名称：path_planner_position_code_to_index
    参  数: position_code - 位置编码（如91表示A9B1）
    返  回: 对应的work_pos数组索引，失败时返回-1
    功能说明：将位置编码转换为work_pos数组索引
    实现逻辑：根据7×9网格布局计算索引
********************************************************/
int path_planner_position_code_to_index(int position_code)
{
    // 位置编码格式：行号×10+列号（如A9B1 = 91）
    int row = position_code / 10;  // 行号（A1=1, A2=2, ..., A9=9）
    int col = position_code % 10;  // 列号（B1=1, B2=2, ..., B7=7）

    // 验证范围
    if (row < 1 || row > 9 || col < 1 || col > 7) {
        return -1; // 无效的位置编码
    }

    // 计算索引：(9-row) * 7 + (col-1)
    // A9B1(91) -> index=0, A9B2(92) -> index=1, A8B1(81) -> index=7
    int index = (9 - row) * 7 + (col - 1);

    // 验证索引范围
    if (index < 0 || index >= WORK_POINT_ARRAY_SIZE) {
        return -1;
    }

    return index;
}
